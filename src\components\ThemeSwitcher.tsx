import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Monitor, Heart, BookOpen } from 'lucide-react';
import { useThemeStore, ThemeType } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';

const themeIcons: Record<ThemeType, React.ComponentType<{ className?: string }>> = {
  modern: Monitor,
  cute: Heart,
};

export const ThemeSwitcher: React.FC = () => {
  const { currentTheme, setTheme } = useThemeStore();
  const { t } = useTranslation();
  
  const themes: ThemeType[] = ['modern', 'cute'];
  
  const getThemeName = (theme: ThemeType): string => {
    switch (theme) {
      case 'modern':
        return t('modernTheme');
      case 'cute':
        return t('cuteTheme');
      default:
        return theme;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
        className="btn-ghost text-sm hover-lift focus-ring"
        aria-label={`当前主题: ${currentTheme === 'modern' ? '现代科技风' : '温馨可爱风'}，点击切换主题`}
        role="button"
        tabIndex={0}
      >
          {t('designStyle')}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {themes.map((theme) => {
          const Icon = themeIcons[theme];
          return (
            <DropdownMenuItem
              key={theme}
              onClick={() => setTheme(theme)}
              className={currentTheme === theme ? 'bg-accent' : ''}
            >
              <Icon className="h-4 w-4 mr-2" />
              {getThemeName(theme)}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};