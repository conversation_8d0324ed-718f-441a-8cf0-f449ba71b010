@tailwind base;
@tailwind components;
@tailwind utilities;

/* 六爻排盘网站设计系统 - 传统与现代融合 */
/* Six Lines Divination Website Design System - Traditional meets Modern */

@layer base {
  :root {
    /* 默认现代科技风主题 */
    --background: 240 10% 4%;
    --foreground: 210 40% 95%;
    --card: 240 8% 8%;
    --card-foreground: 210 40% 95%;
    --popover: 240 8% 8%;
    --popover-foreground: 210 40% 95%;
    --primary: 230 40% 20%;
    --primary-foreground: 210 40% 98%;
    --secondary: 45 90% 55%;
    --secondary-foreground: 230 40% 15%;
    --muted: 240 8% 15%;
    --muted-foreground: 215 20% 65%;
    --accent: 270 50% 25%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 70% 55%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 15% 18%;
    --input: 240 15% 18%;
    --ring: 230 40% 20%;
    --radius: 0.5rem;

    /* 六爻专用色彩 */
    --divination-primary: 230 60% 35%;
    --divination-secondary: 45 95% 60%;
    --divination-accent: 270 55% 30%;
    --divination-success: 142 70% 45%;
    --divination-warning: 38 92% 50%;

    /* 渐变色彩 */
    --gradient-primary: linear-gradient(135deg, hsl(230, 60%, 35%), hsl(270, 55%, 30%));
    --gradient-secondary: linear-gradient(135deg, hsl(45, 95%, 60%), hsl(38, 92%, 50%));
    --gradient-background: linear-gradient(180deg, hsl(240, 8%, 8%), hsl(240, 10%, 4%));
    --gradient-card: linear-gradient(145deg, hsl(240, 8%, 8%), hsl(240, 10%, 6%));

    /* 阴影效果 */
    --shadow-mystical: 0 10px 30px -5px hsl(230 60% 35% / 0.3);
    --shadow-glow: 0 0 40px hsl(45 95% 60% / 0.2);
    --shadow-card: 0 8px 25px -5px hsl(240 10% 4% / 0.4);

    /* 设计系统 - 动画时间 */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

    /* 设计系统 - 间距网格 */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-2xl: 3rem;      /* 48px */
    --spacing-3xl: 4rem;      /* 64px */
    --spacing-4xl: 6rem;      /* 96px */

    /* 设计系统 - 字体大小 */
    --text-xs: 0.75rem;       /* 12px */
    --text-sm: 0.875rem;      /* 14px */
    --text-base: 1rem;        /* 16px */
    --text-lg: 1.125rem;      /* 18px */
    --text-xl: 1.25rem;       /* 20px */
    --text-2xl: 1.5rem;       /* 24px */
    --text-3xl: 1.875rem;     /* 30px */
    --text-4xl: 2.25rem;      /* 36px */
    --text-5xl: 3rem;         /* 48px */

    /* 设计系统 - 行高 */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* 设计系统 - 圆角 */
    --radius-sm: 0.25rem;     /* 4px */
    --radius-md: 0.5rem;      /* 8px */
    --radius-lg: 0.75rem;     /* 12px */
    --radius-xl: 1rem;        /* 16px */
    --radius-2xl: 1.5rem;     /* 24px */
    --radius-full: 9999px;

    /* 设计系统 - Z-index层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;

    /* Sidebar 变量 */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* 可爱暖色风主题 */
  .theme-cute {
    --background: 20 100% 98%;
    --foreground: 20 10% 15%;
    --card: 15 100% 95%;
    --card-foreground: 20 10% 15%;
    --primary: 340 75% 55%;
    --primary-foreground: 0 0% 100%;
    --secondary: 35 95% 65%;
    --secondary-foreground: 20 10% 15%;
    --muted: 20 50% 90%;
    --muted-foreground: 20 10% 40%;
    --accent: 280 60% 70%;
    --accent-foreground: 0 0% 100%;
    --border: 20 30% 85%;
    --divination-primary: 340 75% 55%;
    --divination-secondary: 35 95% 65%;
    --divination-accent: 280 60% 70%;
    --gradient-primary: linear-gradient(135deg, hsl(340, 75%, 55%), hsl(280, 60%, 70%));
    --gradient-secondary: linear-gradient(135deg, hsl(35, 95%, 65%), hsl(45, 100%, 70%));
    --gradient-background: linear-gradient(180deg, hsl(15, 100%, 95%), hsl(20, 100%, 98%));
    --shadow-mystical: 0 10px 30px -5px hsl(340 75% 55% / 0.3);
    --shadow-glow: 0 0 40px hsl(35 95% 65% / 0.3);

    /* 可爱风主题特色设计令牌 */
    --transition-bounce: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    --radius-cute: 1.5rem;    /* 24px - 更圆润的边角 */
    --shadow-cute: 0 8px 32px hsl(340 75% 55% / 0.15);
    --shadow-cute-hover: 0 12px 40px hsl(340 75% 55% / 0.25);

    /* 可爱风色彩扩展 */
    --cute-pink-light: 340 100% 95%;
    --cute-pink-medium: 340 75% 55%;
    --cute-pink-dark: 340 60% 40%;
    --cute-orange-light: 35 100% 90%;
    --cute-orange-medium: 35 95% 65%;
    --cute-purple-light: 280 80% 85%;
    --cute-purple-medium: 280 60% 70%;
  }



  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    background: var(--gradient-background);
    background-attachment: fixed;
    min-height: 100vh;
    transition: var(--transition-smooth);
  }
}

@layer components {
  /* ===== 设计系统组件规范 ===== */

  /* 按钮系统 */
  .btn-primary {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300;
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--shadow-mystical);
  }

  .btn-primary:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  .btn-secondary {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300;
    background: var(--gradient-secondary);
    color: hsl(var(--secondary-foreground));
    box-shadow: 0 4px 15px hsl(45 95% 60% / 0.3);
  }

  .btn-secondary:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  .btn-ghost {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    background: transparent;
    border: 1px solid hsl(var(--border));
    color: hsl(var(--foreground));
  }

  .btn-ghost:hover {
    background: hsl(var(--accent) / 0.1);
    border-color: hsl(var(--accent));
  }

  /* 卡片系统 */
  .card-primary {
    @apply rounded-lg border border-border bg-card text-card-foreground shadow-lg;
    background: var(--gradient-card);
    box-shadow: var(--shadow-card);
    transition: var(--transition-smooth);
  }

  .card-primary:hover {
    box-shadow: var(--shadow-mystical);
    transform: translateY(-2px);
  }

  .card-elevated {
    @apply rounded-xl border border-border bg-card text-card-foreground;
    background: var(--gradient-card);
    box-shadow: 0 20px 40px hsl(var(--foreground) / 0.1);
    transition: var(--transition-smooth);
  }

  .card-elevated:hover {
    box-shadow: 0 25px 50px hsl(var(--foreground) / 0.15);
    transform: translateY(-4px);
  }

  /* 输入框系统 */
  .input-primary {
    @apply w-full px-4 py-3 rounded-lg border border-border bg-background text-foreground;
    transition: var(--transition-smooth);
  }

  .input-primary:focus {
    outline: none;
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
  }

  /* 六爻卡片样式 - 保持向后兼容 */
  .divination-card {
    @apply card-primary;
  }

  /* 神秘按钮样式 */
  .mystical-button {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300;
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--shadow-mystical);
  }

  .mystical-button:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  /* 星空动画效果 */
  @keyframes twinkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
  }

  @keyframes meteor {
    0% {
      opacity: 0;
      transform: translateX(-100px) translateY(-100px) rotate(45deg);
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      opacity: 0;
      transform: translateX(300px) translateY(300px) rotate(45deg);
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.3), 0 0 40px rgba(139, 92, 246, 0.1);
    }
    50% {
      box-shadow: 0 0 30px rgba(139, 92, 246, 0.5), 0 0 60px rgba(139, 92, 246, 0.2);
    }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  /* 金色按钮样式 */
  .golden-button {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300;
    background: var(--gradient-secondary);
    color: hsl(var(--secondary-foreground));
    box-shadow: 0 4px 15px hsl(45 95% 60% / 0.3);
  }

  .golden-button:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  /* 六爻爻位样式 */
  .yao-line {
    @apply w-full h-2 rounded-full transition-all duration-500;
    background: var(--gradient-primary);
    box-shadow: 0 2px 8px hsl(230 60% 35% / 0.4);
  }

  .yao-line.changing {
    background: var(--gradient-secondary);
    box-shadow: var(--shadow-glow);
    animation: pulse 1.5s infinite;
  }

  /* 文字渐变效果 */
  .gradient-text {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent; /* 备用方案 */
  }

  /* 现代科技风渐变文字增强 */
  .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
  }

  .from-white {
    --tw-gradient-from: #ffffff;
    --tw-gradient-to: rgb(255 255 255 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .via-blue-100 {
    --tw-gradient-to: rgb(219 234 254 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), #dbeafe, var(--tw-gradient-to);
  }

  .to-purple-200 {
    --tw-gradient-to: #e9d5ff;
  }

  .bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
  }

  .text-transparent {
    color: transparent;
  }

  /* 发光边框效果 */
  .glow-border {
    @apply border border-border rounded-lg;
    box-shadow: 0 0 20px hsl(230 60% 35% / 0.2);
    transition: var(--transition-smooth);
  }

  .glow-border:hover {
    box-shadow: 0 0 30px hsl(45 95% 60% / 0.3);
  }

  /* ===== 主题特色组件变体 ===== */

  /* 现代科技风主题变体 */
  .theme-modern .card-primary,
  .theme-modern .divination-card {
    backdrop-filter: blur(10px);
    border: 1px solid hsl(var(--border) / 0.8);
    background: linear-gradient(145deg, hsl(240, 8%, 8%), hsl(240, 10%, 6%));
  }

  .theme-modern .btn-primary {
    background: linear-gradient(135deg, hsl(230, 60%, 35%), hsl(270, 55%, 30%));
    box-shadow: 0 8px 25px hsl(230 60% 35% / 0.4);
  }

  .theme-modern .btn-secondary {
    background: linear-gradient(135deg, hsl(45, 95%, 60%), hsl(38, 92%, 50%));
    box-shadow: 0 8px 25px hsl(45 95% 60% / 0.4);
  }

  .theme-modern .input-primary {
    background: hsl(240, 8%, 8%);
    border: 1px solid hsl(var(--border) / 0.8);
    backdrop-filter: blur(10px);
  }

  /* 现代科技风渐变文字强化 */
  .theme-modern h1.bg-gradient-to-r {
    background: linear-gradient(to right, #ffffff, #dbeafe, #e9d5ff) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    color: transparent !important;
  }

  .theme-modern .text-gray-300 {
    color: #d1d5db !important;
  }

  /* 可爱温馨风主题变体 */
  .theme-cute .card-primary,
  .theme-cute .divination-card {
    border-radius: var(--radius-cute);
    box-shadow: var(--shadow-cute);
    background: linear-gradient(145deg, hsl(15, 100%, 95%), hsl(20, 100%, 98%));
  }

  .theme-cute .card-primary:hover,
  .theme-cute .divination-card:hover {
    box-shadow: var(--shadow-cute-hover);
    transform: translateY(-3px) scale(1.02);
  }

  .theme-cute .btn-primary {
    border-radius: var(--radius-cute);
    background: linear-gradient(135deg, hsl(340, 75%, 55%), hsl(280, 60%, 70%));
    box-shadow: 0 8px 25px hsl(340 75% 55% / 0.3);
  }

  .theme-cute .btn-secondary {
    border-radius: var(--radius-cute);
    background: linear-gradient(135deg, hsl(35, 95%, 65%), hsl(45, 100%, 70%));
    box-shadow: 0 8px 25px hsl(35 95% 65% / 0.3);
  }

  .theme-cute .btn-ghost {
    border-radius: var(--radius-cute);
    border: 2px solid hsl(var(--cute-pink-medium));
    color: hsl(var(--cute-pink-dark));
  }

  .theme-cute .btn-ghost:hover {
    background: hsl(var(--cute-pink-light));
    border-color: hsl(var(--cute-pink-medium));
    transform: translateY(-1px) scale(1.05);
  }

  .theme-cute .input-primary {
    border-radius: var(--radius-cute);
    border: 2px solid hsl(var(--cute-pink-light));
    background: hsl(15, 100%, 98%);
  }

  .theme-cute .input-primary:focus {
    border-color: hsl(var(--cute-pink-medium));
    box-shadow: 0 0 0 4px hsl(var(--cute-pink-medium) / 0.1);
  }

  /* 可爱风渐变文字强化 */
  .theme-cute .gradient-text {
    background: linear-gradient(135deg, hsl(340, 75%, 55%), hsl(280, 60%, 70%), hsl(35, 95%, 65%)) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    color: transparent !important;
  }

  .theme-cute .text-pink-700 {
    color: hsl(340, 60%, 35%) !important;
  }



  /* 按钮主题变体 */
  .theme-cute .golden-button {
    border-radius: 2rem;
    box-shadow: 0 6px 20px hsl(340 75% 55% / 0.3);
  }



  /* 可爱风格特殊边框 */
  .theme-cute .border-3 {
    border-width: 3px;
  }

  /* 可爱风格Hero区域增强效果 */
  .theme-cute .hero-enhanced {
    background-attachment: fixed;
  }

  .theme-cute .hero-enhanced .gradient-text {
    background: linear-gradient(135deg, hsl(340, 75%, 55%), hsl(280, 60%, 70%), hsl(35, 95%, 65%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes mystical-glow {
  0%, 100% {
    box-shadow: 0 0 20px hsl(230 60% 35% / 0.2);
  }
  50% {
    box-shadow: 0 0 40px hsl(45 95% 60% / 0.4);
  }
}

@keyframes meteor {
  0% {
    opacity: 0;
    transform: translateX(-100px) translateY(-100px);
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateX(100px) translateY(100px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(5deg);
  }
  66% {
    transform: translateY(5px) rotate(-3deg);
  }
}

/* ===== 增强微交互规范 ===== */

/* 悬停提升效果 - 增强版 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.15),
    0 4px 15px rgba(0, 0, 0, 0.1);
}

.hover-lift:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.03) 0%,
    hsl(var(--secondary) / 0.03) 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* 缩放效果 - 增强版 */
.hover-scale {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.hover-scale:hover {
  transform: scale(1.03);
  filter: brightness(1.02);
}

/* 发光效果 - 增强版 */
.hover-glow {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.hover-glow:hover {
  box-shadow:
    0 0 25px hsl(var(--primary) / 0.4),
    0 0 50px hsl(var(--primary) / 0.2),
    0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow:hover::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg,
    transparent 30%,
    hsl(var(--primary) / 0.08) 50%,
    transparent 70%);
  transform: rotate(45deg);
  animation: shimmer 2s infinite;
}

/* 点击缩放效果 - 增强版 */
.click-scale {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.click-scale:active {
  transform: scale(0.96);
  filter: brightness(0.95);
}

/* 聚焦环效果 - 增强版 */
.focus-ring {
  transition: all 0.2s ease;
  position: relative;
}

.focus-ring:focus {
  outline: none;
  box-shadow:
    0 0 0 3px hsl(var(--primary) / 0.3),
    0 0 0 6px hsl(var(--primary) / 0.1);
}

.focus-ring:focus::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid hsl(var(--primary) / 0.5);
  border-radius: inherit;
  animation: focus-pulse 2s infinite;
}

/* ===== 新增微交互效果 ===== */

/* 波纹点击效果 */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: hsl(var(--primary) / 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
  width: 300px;
  height: 300px;
}

/* 呼吸动画效果 */
.breathe {
  animation: breathe 3s ease-in-out infinite;
}

/* 磁性悬停效果 */
.magnetic-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.magnetic-hover:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 边框动画效果 */
.border-animate {
  position: relative;
  border: 2px solid transparent;
  background: linear-gradient(var(--background), var(--background)) padding-box,
              linear-gradient(45deg, hsl(var(--primary)), hsl(var(--secondary))) border-box;
  transition: all 0.3s ease;
}

.border-animate:hover {
  background: linear-gradient(var(--background), var(--background)) padding-box,
              linear-gradient(45deg, hsl(var(--secondary)), hsl(var(--primary))) border-box;
}

/* ===== 增强动画关键帧 ===== */

/* 焦点脉冲动画 */
@keyframes focus-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02);
  }
}

/* 微妙呼吸动画 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.01);
    opacity: 0.95;
  }
}

/* 波纹扩散动画 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 加载动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-spin {
  animation: spin 1s linear infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.loading-bounce {
  animation: bounce 1.5s ease-in-out infinite;
}

/* 主题特色动画 */
.theme-cute * {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.theme-modern * {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式设计断点提示 */
@media (max-width: 640px) {
  .responsive-text {
    font-size: var(--text-sm);
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .responsive-text {
    font-size: var(--text-base);
  }
}

@media (min-width: 769px) {
  .responsive-text {
    font-size: var(--text-lg);
  }
}

/* ===== 移动端交互优化 ===== */
@media (max-width: 768px) {
  /* 移动端容器优化 */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* 移动端按钮触摸优化 */
  .btn-primary,
  .btn-secondary,
  .btn-ghost,
  .btn-outline {
    min-height: 48px; /* Apple HIG 推荐的最小触摸目标 */
    padding: 12px 24px;
    font-size: 16px; /* 防止iOS Safari自动缩放 */
    touch-action: manipulation; /* 优化触摸响应 */
    -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
  }

  /* 移动端输入框优化 */
  .input-primary,
  .input-secondary {
    min-height: 48px;
    padding: 12px 16px;
    font-size: 16px; /* 防止iOS Safari自动缩放 */
    touch-action: manipulation;
    -webkit-appearance: none; /* 移除默认样式 */
  }

  /* 移动端文本区域优化 */
  textarea.input-primary {
    min-height: 120px; /* 更大的触摸区域 */
    resize: none; /* 禁用调整大小 */
  }

  /* 移动端卡片间距优化 */
  .card-primary,
  .card-secondary,
  .divination-card {
    margin: 8px 0;
    padding: 16px;
  }

  /* 移动端导航菜单优化 */
  .mobile-nav-item {
    min-height: 48px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    font-size: 16px;
    touch-action: manipulation;
  }

  /* 移动端间距系统 */
  .mobile-spacing-xs { padding: 8px; }
  .mobile-spacing-sm { padding: 12px; }
  .mobile-spacing-md { padding: 16px; }
  .mobile-spacing-lg { padding: 20px; }
  .mobile-spacing-xl { padding: 24px; }

  /* 移动端字体大小优化 */
  .mobile-text-sm { font-size: 14px; }
  .mobile-text-base { font-size: 16px; }
  .mobile-text-lg { font-size: 18px; }
  .mobile-text-xl { font-size: 20px; }

  /* 移动端悬停效果禁用（避免粘滞） */
  .hover-lift:hover,
  .hover-scale:hover,
  .hover-glow:hover {
    transform: none;
    box-shadow: inherit;
  }

  /* 移动端点击反馈增强 */
  .mobile-tap-feedback {
    transition: all 0.1s ease;
  }

  .mobile-tap-feedback:active {
    transform: scale(0.98);
    opacity: 0.8;
  }

  /* 移动端字体优化 - 只针对基础元素，不影响主题样式 */
  .mobile-base-text h1 {
    font-size: 2.5rem; /* 40px */
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  .mobile-base-text h2 {
    font-size: 2rem; /* 32px */
    line-height: 1.3;
    margin-bottom: 0.875rem;
  }

  .mobile-base-text h3 {
    font-size: 1.5rem; /* 24px */
    line-height: 1.4;
    margin-bottom: 0.75rem;
  }

  .mobile-base-text h4 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.4;
    margin-bottom: 0.5rem;
  }

  .mobile-base-text p {
    font-size: 1rem; /* 16px */
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  /* 移动端文字大小调整 - 不使用!important避免覆盖主题样式 */
  @media (max-width: 640px) {
    .text-6xl, .text-7xl {
      font-size: 2.5rem; /* 40px on mobile */
    }

    .text-5xl {
      font-size: 2rem; /* 32px on mobile */
    }

    .text-4xl {
      font-size: 1.875rem; /* 30px on mobile */
    }

    .text-3xl {
      font-size: 1.5rem; /* 24px on mobile */
    }

    .text-2xl {
      font-size: 1.25rem; /* 20px on mobile */
    }

    .text-xl {
      font-size: 1.125rem; /* 18px on mobile */
    }
  }

  /* 移动端可读性增强 */
  .mobile-readable {
    font-size: 16px !important;
    line-height: 1.6 !important;
    letter-spacing: 0.01em;
    word-spacing: 0.05em;
  }

  .mobile-title {
    font-size: 24px !important;
    line-height: 1.3 !important;
    font-weight: 600 !important;
    letter-spacing: -0.01em;
  }

  .mobile-subtitle {
    font-size: 18px !important;
    line-height: 1.4 !important;
    font-weight: 500 !important;
  }

  /* 移动端性能优化 */
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 硬件加速优化 */
  .hardware-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  /* 动画性能优化 */
  .optimized-animation {
    will-change: transform, opacity;
    transform: translateZ(0);
  }

  /* 减少重绘和回流 */
  .no-reflow {
    contain: layout style paint;
  }

  /* 图片懒加载优化 */
  img {
    content-visibility: auto;
    contain-intrinsic-size: 300px 200px;
  }

  /* 滚动性能优化 */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 减少动画在低性能设备上的影响 */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

