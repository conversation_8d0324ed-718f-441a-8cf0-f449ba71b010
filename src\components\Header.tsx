import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { User, LogIn, LogOut, UserCircle, Zap, Heart, Menu } from 'lucide-react';
import { useAuthStore } from '@/stores/authStore';
import { AuthModal } from './AuthModal';
import { ThemeSwitcher } from './ThemeSwitcher';
import { LanguageSwitcher } from './LanguageSwitcher';
import { useTranslation } from '@/hooks/useTranslation';
import { useNavigate } from 'react-router-dom';
import { useThemeStore } from '@/stores/themeStore';
import { cn } from '@/lib/utils';

export const Header: React.FC = () => {
  const { user, loading, signOut } = useAuthStore();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { currentTheme } = useThemeStore();

  const handleSignOut = async () => {
    await signOut();
  };

  const handleProfileClick = () => {
    navigate('/profile');
  };

  // 获取主题图标
  const getThemeIcon = () => {
    switch (currentTheme) {
      case 'modern': return Zap;
      case 'cute': return Heart;
      default: return Monitor;
    }
  };

  const ThemeIcon = getThemeIcon();

  // 主题差异化样式
  const getHeaderStyles = () => {
    const baseStyles = "sticky top-0 z-50 w-full backdrop-blur-md transition-all duration-300";

    switch (currentTheme) {
      case 'modern':
        return cn(
          baseStyles,
          "border-b border-slate-800/50 bg-slate-900/80",
          "shadow-lg shadow-blue-500/10"
        );
      case 'cute':
        return cn(
          baseStyles,
          "border-b border-pink-200/50 bg-pink-50/80",
          "shadow-lg shadow-pink-300/20"
        );
      default:
        return cn(baseStyles, "border-b border-border/50 bg-card/30");
    }
  };

  const getContainerStyles = () => {
    const baseStyles = "max-w-7xl mx-auto flex items-center justify-between transition-all duration-300";

    switch (currentTheme) {
      case 'modern':
        return cn(baseStyles, "px-6 py-4");
      case 'cute':
        return cn(baseStyles, "px-4 py-3");
      default:
        return cn(baseStyles, "px-6 py-4");
    }
  };

  const getLogoStyles = () => {
    const baseStyles = "font-bold cursor-pointer transition-all duration-300 flex items-center gap-3";

    switch (currentTheme) {
      case 'modern':
        return cn(
          baseStyles,
          "text-2xl text-blue-400 hover:text-blue-300",
          "hover:scale-105"
        );
      case 'cute':
        return cn(
          baseStyles,
          "text-xl text-pink-600 hover:text-pink-500",
          "hover:scale-105"
        );
      default:
        return cn(baseStyles, "text-2xl gradient-text");
    }
  };

  const getBadgeStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "bg-blue-500/20 text-blue-300 border-blue-400/30 text-xs font-medium";
      case 'cute':
        return "bg-pink-100 text-pink-600 border-pink-200 text-xs font-medium rounded-full";
      default:
        return "text-xs";
    }
  };

  const getNavStyles = () => {
    const baseStyles = "flex items-center transition-all duration-300";

    switch (currentTheme) {
      case 'modern':
        return cn(baseStyles, "space-x-6");
      case 'cute':
        return cn(baseStyles, "space-x-4");
      default:
        return cn(baseStyles, "space-x-6");
    }
  };

  const getButtonStyles = (variant: 'ghost' | 'default' = 'ghost') => {
    switch (currentTheme) {
      case 'modern':
        if (variant === 'default') {
          return "btn-primary hover-lift";
        }
        return "btn-ghost hover-lift";
      case 'cute':
        if (variant === 'default') {
          return "btn-primary hover-lift";
        }
        return "btn-ghost hover-lift";
      default:
        return variant === 'default' ? "btn-primary" : "btn-ghost";
    }
  };

  return (
    <>
      <header className={getHeaderStyles()}>
        <div className={getContainerStyles()}>
          {/* Logo区域 - 主题差异化设计 */}
          <div className="flex items-center space-x-4">
            <div className={getLogoStyles()} onClick={() => navigate('/')}>
              <ThemeIcon className={cn(
                "transition-all duration-300",
                currentTheme === 'modern' ? "w-7 h-7 text-blue-400" : "w-6 h-6 text-pink-500"
              )} />
              <span>{t('appName')}</span>
            </div>
            <Badge
              variant="secondary"
              className={getBadgeStyles()}
            >
              {t('aiAnalysis')}
            </Badge>
          </div>

          {/* 导航区域 - 响应式布局 */}
          <nav className={getNavStyles()}>
            {/* 桌面端导航 */}
            <div className="hidden md:flex items-center space-x-4">
              <LanguageSwitcher />
              <ThemeSwitcher />

              <Button
                variant="ghost"
                onClick={() => navigate('/pricing')}
                className={cn("text-sm", getButtonStyles('ghost'))}
              >
                {t('pricing')}
              </Button>

              <Button
                variant="ghost"
                onClick={() => navigate('/design-system')}
                className={cn("text-sm", getButtonStyles('ghost'))}
              >
                设计系统
              </Button>
            </div>

            {/* 用户认证区域 */}
            <div className="flex items-center space-x-3">
              {loading ? (
                <Button
                  variant="ghost"
                  disabled
                  className={getButtonStyles('ghost')}
                >
                  {t('loading')}
                </Button>
              ) : user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className={cn("text-sm", getButtonStyles('ghost'))}
                    >
                      {t('profile')}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className={cn(
                    "w-48",
                    currentTheme === 'modern' ? "bg-slate-800 border-slate-700" :
                    currentTheme === 'cute' ? "bg-white border-pink-200 rounded-xl" : ""
                  )}>
                    <DropdownMenuItem onClick={handleProfileClick}>
                      <UserCircle className="h-4 w-4 mr-2" />
                      {t('profilePage')}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleSignOut}>
                      <LogOut className="h-4 w-4 mr-2" />
                      {t('logout')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Button
                  variant="default"
                  onClick={() => setAuthModalOpen(true)}
                  className={cn(getButtonStyles('default'), 'focus-ring')}
                  aria-label="打开登录注册对话框"
                  role="button"
                >
                  {t('login')}
                </Button>
              )}

              {/* 移动端菜单按钮 */}
              <div className="md:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(getButtonStyles('ghost'), 'focus-ring p-3')}
                      aria-label="打开移动端导航菜单"
                      role="button"
                    >
                      <Menu className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className={cn(
                    "w-56 p-2",
                    currentTheme === 'modern' ? "bg-slate-800 border-slate-700" :
                    currentTheme === 'cute' ? "bg-white border-pink-200 rounded-xl" : ""
                  )}>
                    {/* 导航链接 */}
                    <DropdownMenuItem
                      onClick={() => navigate('/pricing')}
                      className="py-3 px-4 text-base cursor-pointer hover:bg-opacity-80 focus:bg-opacity-80"
                    >
                      <span className="font-medium">{t('pricing')}</span>
                    </DropdownMenuItem>

                    <DropdownMenuItem
                      onClick={() => navigate('/design-system')}
                      className="py-3 px-4 text-base cursor-pointer hover:bg-opacity-80 focus:bg-opacity-80"
                    >
                      <span className="font-medium">设计系统</span>
                    </DropdownMenuItem>

                    {/* 分隔线 */}
                    <div className={cn(
                      "my-2 h-px",
                      currentTheme === 'modern' ? "bg-slate-600" :
                      currentTheme === 'cute' ? "bg-pink-100" : "bg-border"
                    )} />

                    {/* 设置选项 */}
                    <DropdownMenuItem asChild>
                      <div className="flex items-center justify-between w-full py-3 px-4">
                        <span className="font-medium">{t('designStyle')}</span>
                        <ThemeSwitcher />
                      </div>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <div className="flex items-center justify-between w-full py-3 px-4">
                        <span className="font-medium">{t('language')}</span>
                        <LanguageSwitcher />
                      </div>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </nav>
        </div>
      </header>

      <AuthModal open={authModalOpen} onOpenChange={setAuthModalOpen} />
    </>
  );
};