import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useLanguageStore, Language } from '@/stores/languageStore';
import { Globe } from 'lucide-react';

export const LanguageSwitcher: React.FC = () => {
  const { currentLanguage, setLanguage } = useLanguageStore();
  
  const toggleLanguage = () => {
    const newLanguage: Language = currentLanguage === 'zh' ? 'en' : 'zh';
    setLanguage(newLanguage);
  };
  
  return (
    <Button
      onClick={toggleLanguage}
      className="btn-ghost flex items-center space-x-2 hover-lift focus-ring"
      aria-label={`当前语言: ${currentLanguage === 'zh' ? '中文' : 'English'}，点击切换语言`}
      role="button"
      tabIndex={0}
    >
      <Globe className="h-4 w-4" />
      <span className="text-sm font-medium">
        {currentLanguage === 'zh' ? '中文' : 'EN'}
      </span>
    </Button>
  );
};